import { StatusCodes, throwError } from '@/common/errors/statusCodes'
import { prisma } from '@repo/database'
import { EnterpriseService } from './enterprise.service'

// Constants definition
const MAX_TAGS_PER_ENTERPRISE = 30
const MAX_TAG_NAME_LENGTH = 30

// Enterprise tag management service
export const enterpriseTagService = {
  /**
   * Create enterprise tag
   */
  async createEnterpriseTag(
    enterpriseId: string,
    operatorId: string,
    params: {
      name: string
      color?: string
    },
  ) {
    await EnterpriseService.getInstance().checkIsEnterpriseAdmin(operatorId, enterpriseId)

    if (params.name.length > MAX_TAG_NAME_LENGTH) {
      throwError(
        StatusCodes.BAD_REQUEST,
        `tag name cannot exceed ${MAX_TAG_NAME_LENGTH} characters`,
      )
    }

    const existingTagsCount = await prisma.enterpriseTag.count({
      where: { enterpriseId },
    })

    if (existingTagsCount >= MAX_TAGS_PER_ENTERPRISE) {
      throwError(
        StatusCodes.BAD_REQUEST,
        `enterprise tag count cannot exceed ${MAX_TAGS_PER_ENTERPRISE}`,
      )
    }

    // Check if tag name already exists in the same enterprise
    const existingTag = await prisma.enterpriseTag.findUnique({
      where: {
        enterpriseId_name: {
          enterpriseId,
          name: params.name,
        },
      },
    })

    if (existingTag) {
      throwError(StatusCodes.CONFLICT, 'tag is already exists')
    }

    return await prisma.enterpriseTag.create({
      data: {
        name: params.name,
        color: params.color || 'BLUE',
        enterpriseId,
        createdBy: operatorId,
      },
    })
  },

  /**
   * Get enterprise tag list
   */
  async getEnterpriseTags(enterpriseId: string, operatorId: string) {
    const tags = await prisma.enterpriseTag.findMany({
      where: { enterpriseId },
      orderBy: { createdAt: 'desc' },
    })

    return tags.map((tag) => ({
      id: tag.id,
      name: tag.name,
      color: tag.color,
      createdAt: tag.createdAt,
      updatedAt: tag.updatedAt,
    }))
  },

  /**
   * Delete enterprise tag
   */
  async deleteEnterpriseTag(enterpriseId: string, tagId: string, operatorId: string) {
    await EnterpriseService.getInstance().checkIsEnterpriseAdmin(operatorId, enterpriseId)

    const existingTag = await prisma.enterpriseTag.findFirst({
      where: {
        id: tagId,
        enterpriseId,
      },
    })

    if (!existingTag) {
      throwError(StatusCodes.NOT_FOUND, 'tag not found')
    }

    // Delete tag (associated user tags will be automatically deleted via onDelete: Cascade)
    await prisma.enterpriseTag.delete({
      where: { id: tagId },
    })

    return { success: true }
  },

  /**
   * Add tag to user
   */
  async addTagToUser(membershipId: string, tagId: string, operatorId: string) {
    // Get user membership information
    const membership = await prisma.userMembership.findUnique({
      where: { id: membershipId },
    })

    if (!membership || !membership.enterpriseId || membership.enterpriseId === '') {
      throwError(StatusCodes.NOT_FOUND, 'user not found or not in enterprise')
    }

    await EnterpriseService.getInstance().checkIsEnterpriseAdmin(
      operatorId,
      membership.enterpriseId,
    )

    // Verify tag exists and belongs to the same enterprise
    const tag = await prisma.enterpriseTag.findFirst({
      where: {
        id: tagId,
        enterpriseId: membership.enterpriseId,
      },
    })

    if (!tag) {
      throwError(StatusCodes.NOT_FOUND, 'tag not found')
    }

    // Check if user already has this tag
    const existingTag = await prisma.userMembershipTag.findUnique({
      where: {
        userMembershipId_tagId: {
          userMembershipId: membershipId,
          tagId,
        },
      },
    })

    if (existingTag) {
      throwError(StatusCodes.CONFLICT, 'user already has this tag')
    }

    // Add tag
    return await prisma.userMembershipTag.create({
      data: {
        userMembershipId: membershipId,
        tagId,
        createdBy: operatorId,
      },
      include: {
        enterpriseTag: {
          select: {
            id: true,
            name: true,
            color: true,
          },
        },
      },
    })
  },

  /**
   * Remove tag from user
   */
  async removeTagFromUser(membershipId: string, tagId: string, operatorId: string) {
    const membership = await prisma.userMembership.findUniqueOrThrow({
      where: { id: membershipId },
    })

    if (!membership || !membership.enterpriseId || membership.enterpriseId === '') {
      throwError(StatusCodes.NOT_FOUND, 'user not found or not in enterprise')
    }

    await EnterpriseService.getInstance().checkIsEnterpriseAdmin(
      operatorId,
      membership.enterpriseId,
    )

    const userTag = await prisma.userMembershipTag.findUnique({
      where: {
        userMembershipId_tagId: {
          userMembershipId: membershipId,
          tagId,
        },
      },
    })

    if (!userTag) {
      throwError(StatusCodes.NOT_FOUND, 'user does not have this tag')
    }

    await prisma.userMembershipTag.delete({
      where: {
        userMembershipId_tagId: {
          userMembershipId: membershipId,
          tagId,
        },
      },
    })

    return { success: true }
  },

  /**
   * Get all tags of a user
   */
  async getMembershipTags(membershipId: string, operatorId: string) {
    // Get user membership information
    const membership = await prisma.userMembership.findUniqueOrThrow({
      where: { id: membershipId },
      select: {
        enterpriseId: true,
      },
    })

    if (!membership.enterpriseId || membership.enterpriseId === '') {
      throwError(StatusCodes.NOT_FOUND, 'user not found or not in enterprise')
    }

    // Verify operator is enterprise admin
    await EnterpriseService.getInstance().checkIsEnterpriseAdmin(
      operatorId,
      membership.enterpriseId,
    )

    // Get all tags of the user
    const userTags = await prisma.userMembershipTag.findMany({
      where: {
        userMembershipId: membershipId,
      },
      include: {
        enterpriseTag: {
          select: {
            id: true,
            name: true,
            color: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    })

    return userTags.map((ut) => ({
      id: ut.enterpriseTag.id,
      name: ut.enterpriseTag.name,
      color: ut.enterpriseTag.color,
      taggedAt: ut.createdAt,
      taggedBy: ut.createdBy,
    }))
  },
}
