import { <PERSON>C<PERSON>, throwError } from '@/common/errors/statusCodes'
import { EASYKOL_WARNING_CHANNEL } from '@/config/env'
import { DEFAULT_ALLOWED_EMAILS } from '@/config/whiteList'
import { QuotaCost } from '@/enums/QuotaCost'
import { SlackClient } from '@/infras/monitoring/slackClient'
import {
  AddEnterpriseMembersParams,
  AddEnterpriseMembersResult,
  CreateEnterpriseParams,
  GetEnterprisesParams,
  UpdateEnterpriseBasicInfoParams,
  UpdateEnterpriseParams,
} from '@/types/enterprise'
import { ERROR_MESSAGES, FREE_QUOTA_STRATEGY } from '@/types/memberShip'
import { cache } from '@/utils/cache'
import {
  CardSubscriptionStatus,
  EnterpriseStatus,
  MemberStatus,
  MemberType,
  QuotaType,
  prisma,
} from '@repo/database'
import Bluebird from 'bluebird'
import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone.js'
import utc from 'dayjs/plugin/utc.js'
import { MembershipService } from './membership.service'
import { cancelSubscription } from './stripe/subscription'
import { tagService } from './tag_and_note/tag'
import { TimezoneService } from './timezone.service'
dayjs.extend(utc)
dayjs.extend(timezone)

export class EnterpriseService {
  private static instance: EnterpriseService
  private constructor() {}

  public static getInstance(): EnterpriseService {
    if (!EnterpriseService.instance) {
      EnterpriseService.instance = new EnterpriseService()
    }
    return EnterpriseService.instance
  }

  // 更新企业成员信息
  private async updateMembershipToEnterprise(
    tx: any,
    userId: string,
    enterpriseId: string,
    effectiveAt: Date,
    expireAt: Date,
    accountQuota: number,
    isAdmin: boolean = false,
    usedQuota: number = 0,
    dailyUsage: number = 0,
  ) {
    return await tx.userMembership.update({
      where: { userId },
      data: {
        type: MemberType.ENTERPRISE,
        enterpriseId,
        isEnterpriseAdmin: isAdmin,
        accountQuota,
        usedQuota,
        dailyUsage,
        effectiveAt,
        expireAt,
        status: MemberStatus.ACTIVE,
      },
    })
  }

  // 创建企业
  async createEnterprise(params: CreateEnterpriseParams, operatorId: string) {
    return await prisma.$transaction(
      async (tx) => {
        // 1. 创建企业
        const enterprise = await tx.enterprise.create({
          data: {
            name: params.name,
            description: params.description,
            accountQuota: params.accountQuota,
            dailyLimit: params.dailyLimit,
            memberUsageDailyLimit: params.memberUsageDailyLimit,
            effectiveAt: params.effectiveAt || new Date(),
            expireAt: params.expireAt,
            contactPerson: params.contactPerson,
            contactPhone: params.contactPhone,
            contactEmail: params.contactEmail,
            industry: params.industry,
            scale: params.scale,
            address: params.address,
          },
        })

        // 2. 批量更新用户为企业会员
        if (params.memberEmails?.length) {
          const users = await tx.userInfo.findMany({
            where: { email: { in: params.memberEmails } },
            include: { membership: true },
          })
          // 将用户从免费会员或者付费用户更新为企业会员时，当天的useage重置为0.
          await Promise.all(
            users.map((user) =>
              this.updateMembershipToEnterprise(
                tx,
                user.userId,
                enterprise.id,
                params.effectiveAt || new Date(),
                params.expireAt,
                params.accountQuota,
                params.adminEmails?.includes(user.email || ''),
              ),
            ),
          )
        }

        // 3. 记录操作日志
        await tx.quotaLog.create({
          data: {
            userId: operatorId,
            membershipId: enterprise.id,
            usage: params.accountQuota,
            type: QuotaType.ADMIN,
            description: `Created enterprise: ${enterprise.name}`,
            createdBy: operatorId,
            enterpriseId: enterprise.id,
            metadata: {
              action: 'CREATE_ENTERPRISE',
              memberEmails: params.memberEmails,
              adminEmails: params.adminEmails,
            },
          },
        })

        // 4. 初始化默认的 tags
        await tagService.tryInitDefaultTags(operatorId, enterprise.id)
        // 管理员的企业缓存失效
        await cache.del(`user:${operatorId}:enterpriseId`)

        return enterprise
      },
      { timeout: 10_000 },
    )
  }

  // 更新企业
  async updateEnterprise(id: string, params: UpdateEnterpriseParams, operatorId: string) {
    return await prisma.$transaction(
      async (tx) => {
        // 首先查找企业
        const targetEnterprise = await tx.enterprise.findUnique({ where: { id } })

        if (!targetEnterprise) {
          throw new Error(ERROR_MESSAGES.ENTERPRISE_NOT_FOUND)
        }

        // 如企业过期，但是更新时传入了新的过期时间>当前时间，则将企业状态更新为ACTIVE
        if (
          params.expireAt &&
          dayjs(params.expireAt).isAfter(dayjs()) &&
          targetEnterprise.status === EnterpriseStatus.EXPIRED
        ) {
          params.status = EnterpriseStatus.ACTIVE
        }

        // 更新企业
        const enterprise = await tx.enterprise.update({
          where: { id: targetEnterprise.id },
          data: params,
        })

        // 如果更新了配额或有效期，同步更新所有企业成员
        if (params.accountQuota || params.expireAt) {
          const members = await tx.userMembership.findMany({
            where: { enterpriseId: id },
          })

          await Promise.all(
            members.map((member) =>
              this.updateMembershipToEnterprise(
                tx,
                member.userId,
                id,
                params.effectiveAt || member.effectiveAt,
                params.expireAt || member.expireAt,
                params.accountQuota || member.accountQuota,
                member.isEnterpriseAdmin,
                member.usedQuota,
                member.dailyUsage,
              ),
            ),
          )
        }

        await tx.quotaLog.create({
          data: {
            userId: operatorId,
            membershipId: id,
            usage: params.accountQuota || 0,
            type: QuotaType.ADMIN,
            description: `Updated enterprise: ${enterprise.name}`,
            createdBy: operatorId,
            enterpriseId: id,
            metadata: {
              action: 'UPDATE_ENTERPRISE',
              updates: params,
            },
          },
        })

        return enterprise
      },
      { timeout: 10_000 },
    )
  }

  // 移除企业成员
  async removeEnterpriseMember(enterpriseId: string, userId: string, operatorId: string) {
    return await prisma.$transaction(
      async (tx) => {
        // 1. 获取用户会员信息
        const membership = await tx.userMembership.findUnique({
          where: { userId },
          select: {
            id: true,
            createdAt: true,
            timezone: true,
          },
        })

        if (!membership) {
          throw new Error('Membership not found')
        }

        // 判断操作者是否有权限移除企业成员
        const operatorMembership = await tx.userMembership.findFirst({
          where: {
            userId: operatorId,
          },
          include: {
            user: {
              select: {
                email: true,
              },
            },
          },
        })
        const isEnterpriseAdmin =
          operatorMembership?.isEnterpriseAdmin && operatorMembership?.enterpriseId === enterpriseId
        const isOperatorInWhiteList = DEFAULT_ALLOWED_EMAILS.split(',').includes(
          operatorMembership?.user?.email || '',
        )

        if (!isOperatorInWhiteList && !isEnterpriseAdmin) {
          throw new Error('No permission to update enterprise basic info')
        }

        // 2. 计算用户应该获得的免费额度
        const userTimezone = membership.timezone || 'Asia/Shanghai'
        const { start: todayInUserTz, end: endOfDayInUserTz } =
          TimezoneService.getUserDayRange(userTimezone)

        const daysSinceCreation = dayjs().diff(dayjs(membership.createdAt), 'days')
        let freeQuota = FREE_QUOTA_STRATEGY.REGULAR

        if (daysSinceCreation === 0) {
          freeQuota = FREE_QUOTA_STRATEGY.FIRST_DAY
        } else if (daysSinceCreation <= FREE_QUOTA_STRATEGY.FIRST_WEEK_DAYS) {
          freeQuota = FREE_QUOTA_STRATEGY.FIRST_WEEK
        }
        // 3. 更新用户为免费会员
        const updatedMembership = await tx.userMembership.update({
          where: { userId },
          data: {
            type: MemberType.FREE,
            enterpriseId: null,
            isEnterpriseAdmin: false,
            accountQuota: freeQuota,
            usedQuota: 0,
            dailyUsage: 0,
            effectiveAt: todayInUserTz,
            expireAt: endOfDayInUserTz,
            enableEnterpriseQuotaDailyLimit: false,
            enterpriseQuotaDailyLimit: 0,
          },
        })

        await cache.del(`user:${userId}:enterpriseId`)

        await tagService.tryInitDefaultTags(userId, undefined)
        // 4. 记录操作日志
        await tx.quotaLog.create({
          data: {
            userId,
            membershipId: membership.id,
            usage: freeQuota,
            type: QuotaType.ADMIN,
            description: `Removed from enterprise, converted to free member (${
              daysSinceCreation === 0
                ? 'First day'
                : daysSinceCreation <= FREE_QUOTA_STRATEGY.FIRST_WEEK_DAYS
                  ? 'First week'
                  : 'Regular'
            } quota),dailyUsage reset to 0`,
            createdBy: operatorId,
            enterpriseId,
            metadata: {
              action: 'REMOVE_ENTERPRISE_MEMBER',
              daysSinceCreation,
              quotaStrategy:
                daysSinceCreation === 0
                  ? 'FIRST_DAY'
                  : daysSinceCreation <= FREE_QUOTA_STRATEGY.FIRST_WEEK_DAYS
                    ? 'FIRST_WEEK'
                    : 'REGULAR',
              assignedQuota: freeQuota,
            },
          },
        })

        return updatedMembership
      },
      { timeout: 10_000 },
    )
  }

  // 添加企业成员
  async addEnterpriseMembers(
    id: string,
    params: AddEnterpriseMembersParams,
    operatorId: string,
  ): Promise<AddEnterpriseMembersResult> {
    const enterprise = await prisma.enterprise.findUnique({ where: { id } })
    if (!enterprise) {
      throw new Error(ERROR_MESSAGES.ENTERPRISE_NOT_FOUND)
    }

    const operatorMembership = await prisma.userMembership.findFirst({
      where: {
        userId: operatorId,
      },
      include: {
        user: {
          select: {
            email: true,
          },
        },
      },
    })

    const isEnterpriseAdmin =
      operatorMembership?.isEnterpriseAdmin && operatorMembership?.enterpriseId === id
    const isOperatorInWhiteList = DEFAULT_ALLOWED_EMAILS.split(',').includes(
      operatorMembership?.user?.email || '',
    )

    if (!isOperatorInWhiteList && !isEnterpriseAdmin) {
      throw new Error('No permission to update enterprise basic info')
    }

    // 查找已存在的用户
    const existingUsers = await prisma.userInfo.findMany({
      where: { email: { in: params.emails } },
      include: { membership: true },
    })

    const nonExistentEmails = params.emails.filter(
      (email) => !existingUsers.some((user) => user.email === email),
    )

    const result = await prisma.$transaction(
      async (tx) => {
        // 过滤出不在当前企业的用户
        const usersToAdd = existingUsers.filter((user) => user.membership?.enterpriseId !== id)

        if (usersToAdd.length === 0) {
          return []
        }

        const updates = await Promise.allSettled(
          usersToAdd.map((user) => {
            cache.del(`user:${user.userId}:enterpriseId`)
            return tx.userMembership.update({
              where: { userId: user.userId },
              data: {
                type: MemberType.ENTERPRISE,
                accountQuota: enterprise.accountQuota,
                usedQuota: 0,
                dailyUsage: 0,
                effectiveAt: enterprise.effectiveAt || new Date(),
                expireAt: enterprise.expireAt,
                enterpriseId: id,
                status: MemberStatus.ACTIVE, // 更新为正常状态
              },
            })
          }),
        )

        // 记录操作日志
        await tx.quotaLog.create({
          data: {
            userId: operatorId,
            membershipId: id,
            usage: 0,
            type: QuotaType.ADMIN,
            description: `Added ${updates.length} new members to enterprise: ${enterprise.name},user usedQuota reset to 0 and dailyUsage reset to 0`,
            createdBy: operatorId,
            enterpriseId: id,
            metadata: {
              action: 'ADD_ENTERPRISE_MEMBERS',
              addedEmails: usersToAdd.map((u) => u.email),
              skippedEmails: existingUsers
                .filter((u) => u.membership?.enterpriseId === id)
                .map((u) => u.email),
              nonExistentEmails,
            },
          },
        })

        return updates
      },
      { timeout: 10_000 },
    )

    const successAddedEmails: string[] = []
    const failedAddedEmails: string[] = []
    const members: any[] = []

    result.forEach((r) => {
      if (r.status === 'fulfilled') {
        members.push(r.value)
        const user = existingUsers.find((u) => u.userId === r.value.userId)
        if (user?.email) {
          successAddedEmails.push(user.email)
        }
      } else {
        // 处理更新失败的情况
        const userId = r.reason?.meta?.target?.userId
        const user = existingUsers.find((u) => u.userId === userId)
        if (user?.email) {
          failedAddedEmails.push(user.email)
        }
      }
    })

    failedAddedEmails.push(...nonExistentEmails)

    const existingEmails = existingUsers
      .filter((user) => user.membership?.enterpriseId === id)
      .map((user) => user.email)
      .filter((email): email is string => email !== null)

    // 为成功加入企业的用户取消订阅
    if (successAddedEmails.length > 0) {
      try {
        const successfulUserIds = members.map((member) => member.userId)
        const subscriptions = await prisma.stripeSubscription.findMany({
          where: {
            userId: { in: successfulUserIds },
            status: 'ACTIVE', // 只取消活跃状态的订阅
          },
        })

        if (subscriptions.length > 0) {
          console.log(`找到 ${subscriptions.length} 个需要取消的活跃订阅`)
        }
        // 批量取消订阅
        await Bluebird.map(
          subscriptions,
          async (subscription) => {
            try {
              await cancelSubscription(subscription.stripeSubscriptionId)
            } catch (error) {
              console.error(`取消订阅失败，用户ID: ${subscription.userId}, 错误:`, error)
            }
          },
          { concurrency: 10 },
        )
      } catch (error) {
        console.error('批量取消订阅时发生错误:', error)
      }
    }

    return {
      successAddedEmails,
      failedAddedEmails,
      existingEmails,
      members,
    }
  }

  // 获取企业详情
  async getEnterpriseDetail(id: string) {
    const enterprise = await prisma.enterprise.findUnique({
      where: { id },
      include: {
        members: {
          include: {
            user: {
              select: {
                userId: true,
                email: true,
                createdAt: true,
                updatedAt: true,
              },
            },
            membershipTags: {
              include: {
                enterpriseTag: {
                  select: {
                    id: true,
                    name: true,
                    color: true,
                  },
                },
              },
              orderBy: { createdAt: 'desc' },
            },
          },
        },
      },
    })

    if (!enterprise) {
      return null
    }

    const transformedMembers = enterprise.members.map((member) => {
      const { membershipTags, ...memberWithoutTags } = member
      return {
        ...memberWithoutTags,
        enterpriseTags: membershipTags.map((mt) => mt.enterpriseTag),
      }
    })

    return {
      ...enterprise,
      members: transformedMembers,
    }
  }

  // 获取企业成员列表
  async getEnterpriseMembers(
    enterpriseId: string,
    tagIds?: string[],
    page: number = 1,
    pageSize: number = 20,
  ) {
    const skip = (page - 1) * pageSize

    const whereCondition: any = {
      enterpriseId,
      NOT: { enterpriseId: '' },
    }

    if (tagIds && tagIds.length > 0) {
      whereCondition.membershipTags = {
        some: {
          tagId: { in: tagIds },
        },
      }
    }

    const [members, totalCount] = await Promise.all([
      prisma.userMembership.findMany({
        where: whereCondition,
        include: {
          user: {
            select: {
              userId: true,
              email: true,
              createdAt: true,
              updatedAt: true,
            },
          },
          membershipTags: {
            include: {
              enterpriseTag: {
                select: {
                  id: true,
                  name: true,
                  color: true,
                },
              },
            },
            orderBy: { createdAt: 'desc' },
          },
        },
        skip,
        take: pageSize,
        orderBy: { createdAt: 'desc' },
      }),
      prisma.userMembership.count({
        where: whereCondition,
      }),
    ])

    const transformedMembers = members.map((member) => {
      const { membershipTags, ...memberWithoutTags } = member
      return {
        ...memberWithoutTags,
        enterpriseTags: membershipTags.map((mt) => mt.enterpriseTag),
      }
    })

    return {
      members: transformedMembers,
      page,
      pageSize,
      total: totalCount,
      totalPages: Math.ceil(totalCount / pageSize),
      hasMore: skip + pageSize < totalCount,
    }
  }

  // 获取企业列表
  async getEnterprises(params: GetEnterprisesParams) {
    return await prisma.enterprise.findMany({
      where: {
        status: params.status,
        name: params.keyword ? { contains: params.keyword } : undefined,
      },
      orderBy: { createdAt: 'desc' },
      skip: params.skip,
      take: params.limit,
    })
  }

  // 删除企业
  async deleteEnterprise(id: string, operatorId: string) {
    return await prisma.$transaction(
      async (tx) => {
        const enterprise = await tx.enterprise.findUnique({ where: { id } })
        if (!enterprise) {
          throw new Error(ERROR_MESSAGES.ENTERPRISE_NOT_FOUND)
        }

        const members = await tx.userMembership.findMany({
          where: { enterpriseId: id },
          include: { user: true },
        })

        if (members.length) {
          await Promise.all(
            members.map((member) => {
              cache.del(`user:${member.userId}:enterpriseId`)
              return this.removeEnterpriseMember(id, member.userId, operatorId)
            }),
          )
        }

        const deletedEnterprise = await tx.enterprise.delete({
          where: { id },
        })

        await tx.quotaLog.create({
          data: {
            userId: operatorId,
            membershipId: id,
            usage: 0,
            type: QuotaType.ADMIN,
            description: `Deleted enterprise: ${enterprise.name}`,
            createdBy: operatorId,
            enterpriseId: id,
            metadata: {
              action: 'DELETE_ENTERPRISE',
              memberCount: members.length,
              members: members.map((m) => m.user.email),
            },
          },
        })

        return deletedEnterprise
      },
      { timeout: 10_000 },
    )
  }

  async updateEnterpriseBasicInfo(
    id: string,
    params: UpdateEnterpriseBasicInfoParams,
    operatorId: string,
  ) {
    return await prisma.$transaction(
      async (tx) => {
        const enterprise = await tx.enterprise.findUniqueOrThrow({
          where: { id },
        })

        const operatorMembership = await tx.userMembership.findFirst({
          where: {
            userId: operatorId,
          },
          include: {
            user: {
              select: {
                email: true,
              },
            },
          },
        })
        const isEnterpriseAdmin =
          operatorMembership?.isEnterpriseAdmin && operatorMembership?.enterpriseId === id
        const isOperatorInWhiteList = DEFAULT_ALLOWED_EMAILS.split(',').includes(
          operatorMembership?.user?.email || '',
        )

        if (!isOperatorInWhiteList && !isEnterpriseAdmin) {
          throw new Error('No permission to update enterprise basic info')
        }

        const updatedEnterprise = await tx.enterprise.update({
          where: { id },
          data: params,
        })

        // 记录操作日志
        await tx.quotaLog.create({
          data: {
            userId: operatorId,
            membershipId: id,
            usage: 0,
            type: QuotaType.ADMIN,
            description: `Updated enterprise basic info: ${enterprise.name}`,
            createdBy: operatorId,
            enterpriseId: id,
            metadata: {
              action: 'UPDATE_ENTERPRISE_BASIC_INFO',
              updates: params,
            },
          },
        })

        return updatedEnterprise
      },
      { timeout: 10_000 },
    )
  }

  // 先加入企业才能设置为管理员
  async setEnterpriseAdmin(enterpriseId: string, memberShipId: string, operatorId: string) {
    return await prisma.$transaction(
      async (tx) => {
        const enterprise = await tx.enterprise.findUnique({
          where: {
            id: enterpriseId,
            status: EnterpriseStatus.ACTIVE,
          },
        })

        if (!enterprise) {
          throw new Error(ERROR_MESSAGES.ENTERPRISE_NOT_FOUND)
        }

        const memberShip = await tx.userMembership.findUnique({
          where: {
            id: memberShipId,
            enterpriseId,
          },
        })

        if (!memberShip) {
          throw new Error(ERROR_MESSAGES.MEMBERSHIP_NOT_FOUND)
        }

        if (memberShip.isEnterpriseAdmin && memberShip.enterpriseId === enterpriseId) {
          throw new Error('User is already the enterprise admin')
        }

        const operatorMembership = await tx.userMembership.findFirst({
          where: {
            userId: operatorId,
          },
          include: {
            user: {
              select: {
                email: true,
              },
            },
          },
        })

        const isTheEnterpriseAdmin =
          operatorMembership?.isEnterpriseAdmin && operatorMembership?.enterpriseId === enterpriseId
        const isOperatorInWhiteList = DEFAULT_ALLOWED_EMAILS.split(',').includes(
          operatorMembership?.user?.email || '',
        )
        // 如果操作者又不在企业内，又不在白名单中，则抛出错误没有权限
        if (!isTheEnterpriseAdmin && !isOperatorInWhiteList) {
          throw new Error('No permission to set enterprise admin')
        }

        const updatedMemberShip = await tx.userMembership.update({
          where: { id: memberShipId },
          data: {
            isEnterpriseAdmin: true,
          },
        })

        await tx.quotaLog.create({
          data: {
            userId: operatorId,
            membershipId: memberShipId,
            usage: 0,
            type: QuotaType.ADMIN,
            description: `Set enterprise admin: ${enterprise.name}`,
            createdBy: operatorId,
            enterpriseId: enterpriseId,
            metadata: {
              action: 'SET_ENTERPRISE_ADMIN',
              memberShipId,
            },
          },
        })

        return updatedMemberShip
      },
      { timeout: 10_000 },
    )
  }

  // 开启企业成员配额限制
  async enableMemberQuotaLimit(enterpriseId: string, userId: string, operatorId: string) {
    return await prisma.$transaction(
      async (tx) => {
        // 1. 检查企业是否存在
        const enterprise = await tx.enterprise.findUnique({
          where: {
            id: enterpriseId,
            status: EnterpriseStatus.ACTIVE,
          },
        })

        if (!enterprise) {
          throw new Error(ERROR_MESSAGES.ENTERPRISE_NOT_FOUND)
        }

        // 2. 检查用户是否是企业成员
        const membership = await tx.userMembership.findFirst({
          where: {
            userId,
            enterpriseId,
            status: MemberStatus.ACTIVE,
          },
        })

        if (!membership) {
          throw new Error('用户不是该企业的成员')
        }

        // 3. 检查操作权限
        const operatorMembership = await tx.userMembership.findFirst({
          where: {
            userId: operatorId,
          },
          include: {
            user: {
              select: {
                email: true,
              },
            },
          },
        })

        const isEnterpriseAdmin =
          operatorMembership?.isEnterpriseAdmin && operatorMembership?.enterpriseId === enterpriseId
        const isOperatorInWhiteList = DEFAULT_ALLOWED_EMAILS.split(',').includes(
          operatorMembership?.user?.email || '',
        )

        if (!isOperatorInWhiteList && !isEnterpriseAdmin) {
          throw new Error('没有权限设置企业成员配额限制')
        }

        // 4. 检查企业剩余配额
        if (enterprise.memberUsageDailyLimit > enterprise.accountQuota - enterprise.usedQuota) {
          throw new Error('企业剩余配额不足')
        }

        // 5. 更新成员配额设置
        const updatedMembership = await tx.userMembership.update({
          where: { id: membership.id },
          data: {
            enableEnterpriseQuotaDailyLimit: true,
            enterpriseQuotaDailyLimit: enterprise.memberUsageDailyLimit,
          },
        })

        // 6. 记录操作日志
        await tx.quotaLog.create({
          data: {
            userId: operatorId,
            membershipId: membership.id,
            usage: 0,
            type: QuotaType.ADMIN,
            description: `开启企业成员每日配额限制，限额: ${enterprise.memberUsageDailyLimit}`,
            createdBy: operatorId,
            enterpriseId,
            metadata: {
              action: 'ENABLE_MEMBER_QUOTA_LIMIT',
              targetUserId: userId,
              dailyLimit: enterprise.memberUsageDailyLimit,
            },
          },
        })

        return updatedMembership
      },
      { timeout: 10_000 },
    )
  }

  // 关闭企业成员配额限制
  async disableMemberQuotaLimit(enterpriseId: string, userId: string, operatorId: string) {
    return await prisma.$transaction(
      async (tx) => {
        // 1. 检查企业是否存在
        const enterprise = await tx.enterprise.findUnique({
          where: {
            id: enterpriseId,
            status: EnterpriseStatus.ACTIVE,
          },
        })

        if (!enterprise) {
          throw new Error(ERROR_MESSAGES.ENTERPRISE_NOT_FOUND)
        }

        // 2. 检查用户是否是企业成员
        const membership = await tx.userMembership.findFirst({
          where: {
            userId,
            enterpriseId,
            status: MemberStatus.ACTIVE,
          },
        })

        if (!membership) {
          throw new Error('用户不是该企业的成员')
        }

        // 3. 检查操作权限
        const operatorMembership = await tx.userMembership.findFirst({
          where: {
            userId: operatorId,
          },
          include: {
            user: {
              select: {
                email: true,
              },
            },
          },
        })

        const isEnterpriseAdmin =
          operatorMembership?.isEnterpriseAdmin && operatorMembership?.enterpriseId === enterpriseId
        const isOperatorInWhiteList = DEFAULT_ALLOWED_EMAILS.split(',').includes(
          operatorMembership?.user?.email || '',
        )

        if (!isOperatorInWhiteList && !isEnterpriseAdmin) {
          throw new Error('no permission to disable enterprise member quota limit')
        }

        // 4. 更新成员配额设置
        const updatedMembership = await tx.userMembership.update({
          where: { id: membership.id },
          data: {
            enableEnterpriseQuotaDailyLimit: false,
          },
        })

        // 5. 记录操作日志
        await tx.quotaLog.create({
          data: {
            userId: operatorId,
            membershipId: membership.id,
            usage: 0,
            type: QuotaType.ADMIN,
            description: 'close enterprise member personal quota limit',
            createdBy: operatorId,
            enterpriseId,
            metadata: {
              action: 'DISABLE_MEMBER_QUOTA_LIMIT',
              targetUserId: userId,
            },
          },
        })

        return updatedMembership
      },
      { timeout: 10_000 },
    )
  }

  // update enterprise member quota limit
  async updateMemberQuotaLimit(
    enterpriseId: string,
    userId: string,
    dailyLimit: number,
    operatorId: string,
  ) {
    return await prisma.$transaction(
      async (tx) => {
        // 1. 检查企业是否存在
        const enterprise = await tx.enterprise.findUnique({
          where: {
            id: enterpriseId,
            status: EnterpriseStatus.ACTIVE,
          },
        })

        if (!enterprise) {
          throw new Error(ERROR_MESSAGES.ENTERPRISE_NOT_FOUND)
        }

        // 2. 检查用户是否是企业成员
        const membership = await tx.userMembership.findFirst({
          where: {
            userId,
            enterpriseId,
            status: MemberStatus.ACTIVE,
            enableEnterpriseQuotaDailyLimit: true,
          },
        })

        if (!membership) {
          throw new Error(
            'the user is not a member of the enterprise or the quota limit is not enabled',
          )
        }

        // 3. 检查操作权限
        const operatorMembership = await tx.userMembership.findFirst({
          where: {
            userId: operatorId,
          },
          include: {
            user: {
              select: {
                email: true,
              },
            },
          },
        })

        const isEnterpriseAdmin =
          operatorMembership?.isEnterpriseAdmin && operatorMembership?.enterpriseId === enterpriseId
        const isOperatorInWhiteList = DEFAULT_ALLOWED_EMAILS.split(',').includes(
          operatorMembership?.user?.email || '',
        )

        if (!isOperatorInWhiteList && !isEnterpriseAdmin) {
          throw new Error('no permission to update enterprise member quota limit')
        }

        // 4. 检查新配额是否超过企业剩余配额
        if (dailyLimit > enterprise.accountQuota - enterprise.usedQuota) {
          throw new Error('the quota limit is exceeded the enterprise remaining quota')
        }

        // 5. 更新成员配额设置
        const updatedMembership = await tx.userMembership.update({
          where: { id: membership.id },
          data: {
            enterpriseQuotaDailyLimit: dailyLimit,
          },
        })

        // 6. 记录操作日志
        await tx.quotaLog.create({
          data: {
            userId: operatorId,
            membershipId: membership.id,
            usage: 0,
            type: QuotaType.ADMIN,
            description: `update enterprise member personal daily quota limit to: ${dailyLimit}`,
            createdBy: operatorId,
            enterpriseId,
            metadata: {
              action: 'UPDATE_MEMBER_QUOTA_LIMIT',
              targetUserId: userId,
              oldDailyLimit: membership.enterpriseQuotaDailyLimit,
              newDailyLimit: dailyLimit,
            },
          },
        })

        return updatedMembership
      },
      { timeout: 10_000 },
    )
  }

  // check if the user is the enterprise admin
  async checkIsEnterpriseAdmin(userId: string, enterpriseId: string) {
    const operatorMembership = await prisma.userMembership.findFirst({
      where: {
        userId,
      },
      include: {
        user: {
          select: {
            email: true,
          },
        },
      },
    })

    const isEnterpriseAdmin =
      operatorMembership?.isEnterpriseAdmin && operatorMembership?.enterpriseId === enterpriseId
    const isOperatorInWhiteList = DEFAULT_ALLOWED_EMAILS.split(',').includes(
      operatorMembership?.user?.email || '',
    )

    if (!isOperatorInWhiteList && !isEnterpriseAdmin) {
      throwError(StatusCodes.FORBIDDEN)
    }

    return { isEnterpriseAdmin, isOperatorInWhiteList, operatorMembership }
  }

  // buy info card for enterprise member
  async buyInfoCardForMember(
    enterpriseId: string,
    membershipId: string,
    months: number,
    operatorId: string,
  ) {
    const enterprise = await prisma.enterprise.findUnique({
      where: {
        id: enterpriseId,
        status: EnterpriseStatus.ACTIVE,
      },
    })

    if (!enterprise) {
      throwError(StatusCodes.ENTERPRISE_NOT_FOUND, 'enterprise not found')
    }

    // check member card subscription status
    const membership =
      await MembershipService.getInstance().refreshEnterpriseMembershipInfoCardStatus(membershipId)

    if (!membership) {
      throwError(StatusCodes.MEMBERSHIP_NOT_FOUND, 'membership not found')
    }

    const userInfo = await prisma.userInfo.findUnique({
      where: {
        userId: membership.userId,
      },
    })

    if (!userInfo) {
      throwError(StatusCodes.NOT_FOUND, 'user not found')
    }

    await this.checkIsEnterpriseAdmin(operatorId, enterpriseId)

    const requiredQuota = months * QuotaCost.INFOCARD_SUBSCRIPTION
    const availableQuota = enterprise.accountQuota - enterprise.usedQuota

    if (availableQuota < requiredQuota) {
      throwError(
        StatusCodes.ENTERPRISE_INSUFFICIENT_QUOTA,
        `需要 ${requiredQuota} 点配额，但只有 ${availableQuota} 点可用配额`,
        { requiredQuota, availableQuota },
      )
    }

    const now = dayjs().toDate()
    let effectiveAt: Date
    let expireAt: Date

    // 记录之前的状态用于通知
    const previousStatus = membership.cardSubscriptionStatus || ''
    const previousExpireAt = membership.cardSubscriptionExpireAt
      ? new Date(membership.cardSubscriptionExpireAt)
      : null

    if (
      membership.cardSubscriptionStatus === CardSubscriptionStatus.ACTIVE &&
      membership.cardSubscriptionExpireAt &&
      membership.cardSubscriptionEffectiveAt &&
      dayjs(membership.cardSubscriptionExpireAt).isAfter(dayjs(now)) &&
      dayjs(membership.cardSubscriptionEffectiveAt).isBefore(dayjs(now))
    ) {
      effectiveAt = membership.cardSubscriptionEffectiveAt || now
      expireAt = new Date(membership.cardSubscriptionExpireAt)
      expireAt.setMonth(expireAt.getMonth() + months)
    } else {
      effectiveAt = now
      expireAt = new Date(now)
      expireAt.setMonth(expireAt.getMonth() + months)
    }

    // 将事务操作和Slack通知分离
    let updatedMembership

    try {
      updatedMembership = await prisma.$transaction(
        async (tx) => {
          // 更新会员信息
          const updated = await tx.userMembership.update({
            where: { id: membershipId },
            data: {
              cardSubscriptionStatus: CardSubscriptionStatus.ACTIVE,
              cardSubscriptionEffectiveAt: effectiveAt,
              cardSubscriptionExpireAt: expireAt,
            },
          })

          // 更新企业已使用配额
          await tx.enterprise.update({
            where: { id: enterpriseId },
            data: {
              usedQuota: enterprise.usedQuota + requiredQuota,
            },
          })

          // 日志
          await tx.quotaLog.create({
            data: {
              userId: operatorId,
              membershipId: membershipId,
              usage: requiredQuota,
              type: QuotaType.ADMIN,
              description: `企业管理员为成员${userInfo.email}购买信息卡会员 ${months} 个月`,
              createdBy: operatorId,
              enterpriseId,
              metadata: {
                action: 'INFOCARD_SUBSCRIPTION',
                months,
                effectiveAt: effectiveAt.toISOString(),
                expireAt: expireAt.toISOString(),
                memberEmail: userInfo.email,
              },
            },
          })

          return {
            ...updated,
            requiredQuota,
            months,
          }
        },
        {
          timeout: 10_000,
          isolationLevel: 'Serializable',
        },
      )
    } catch (error) {
      console.error('购买信息卡会员事务失败:', error)
      throw error
    }

    // 事务成功后，再发送Slack通知
    try {
      const formatDate = (date: Date | null): string => {
        if (!date) return '无'
        return date.toLocaleString('zh-CN', {
          timeZone: 'Asia/Shanghai',
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
        })
      }
      const notificationData = {
        企业名称: enterprise.name,
        用户邮箱: userInfo.email,
        购买时长: `${months} 个月`,
        消耗任务次数: `${requiredQuota / 10} 次`,
        之前状态: previousStatus,
        当前状态: CardSubscriptionStatus.ACTIVE,
        之前到期时间: formatDate(previousExpireAt),
        当前到期时间: formatDate(expireAt),
        购买时间: new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' }),
      }
      await SlackClient.getInstance().sendEnterpriseNotification(
        EASYKOL_WARNING_CHANNEL,
        notificationData,
        '🎉 企业信息卡会员购买通知',
        {
          notify_users: true,
        },
      )
    } catch (error) {
      console.error('发送信息卡购买通知失败:', error)
    }

    return updatedMembership
  }

  /**
   * 验证企业成员权限
   * 检查两个用户是否属于同一个企业且状态正常
   * @param currentUserId 当前用户ID
   * @param targetUserId 目标用户ID
   * @throws Error 当用户不存在、状态异常或不属于同一企业时抛出错误
   */
  async validateEnterprisePermission(currentUserId: string, targetUserId: string): Promise<void> {
    const memberships = await prisma.userMembership.findMany({
      where: {
        userId: {
          in: [currentUserId, targetUserId],
        },
      },
      select: {
        userId: true,
        enterpriseId: true,
        type: true,
        status: true,
      },
    })

    if (memberships.length !== 2) {
      throw new Error('user not found')
    }

    const currentUserMembership = memberships.find((m) => m.userId === currentUserId)
    const targetUserMembership = memberships.find((m) => m.userId === targetUserId)

    if (!currentUserMembership || !targetUserMembership) {
      throw new Error('user not found')
    }

    if (
      currentUserMembership.status !== MemberStatus.ACTIVE ||
      targetUserMembership.status !== MemberStatus.ACTIVE
    ) {
      throw new Error('user status is not active')
    }

    if (
      !currentUserMembership.enterpriseId ||
      !targetUserMembership.enterpriseId ||
      currentUserMembership.enterpriseId !== targetUserMembership.enterpriseId
    ) {
      throw new Error('user not in the same enterprise')
    }
  }
}
