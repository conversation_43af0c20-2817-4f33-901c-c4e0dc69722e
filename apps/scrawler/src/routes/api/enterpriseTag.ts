import { successResponse, successResponseWithoutData } from '@/common/response/response'
import { FullResponseSchema } from '@/config/swagger'
import { withAuth } from '@/middlewares/auth'
import {
  AddTagSuccessResponseSchema,
  AddTagToUserRequest,
  AddTagToUserRequestSchema,
  CreateEnterpriseTagRequest,
  CreateEnterpriseTagRequestSchema,
  EnterpriseTagSchema,
  GetEnterpriseTagsResponseSchema,
  GetUserTagsResponseSchema,
  RemoveTagFromUserRequest,
  RemoveTagFromUserRequestSchema,
  SuccessResponseSchema,
} from '@/routes/schemas/enterpriseTag'
import { enterpriseTagService } from '@/services/enterpriseTag.service'
import { FastifyPluginAsync } from 'fastify'

const router: FastifyPluginAsync = async (fastify, opts): Promise<void> => {
  fastify.post<{ Params: { enterpriseId: string }; Body: CreateEnterpriseTagRequest }>(
    '/:enterpriseId/tags',
    {
      schema: {
        tags: ['enterprise-tag'],
        summary: '创建企业标签',
        description: '为企业创建新的标签，用于标记企业内的用户',
        params: {
          type: 'object',
          properties: {
            enterpriseId: { type: 'string', description: '企业ID' },
          },
          required: ['enterpriseId'],
        },
        body: CreateEnterpriseTagRequestSchema,
        response: FullResponseSchema(EnterpriseTagSchema),
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { enterpriseId } = request.params
      const params = request.body
      const operatorId = (request as any).user?.id

      const tag = await enterpriseTagService.createEnterpriseTag(enterpriseId, operatorId, params)
      return reply.status(201).send(successResponse(tag))
    },
  )

  // 获取企业标签列表
  fastify.get<{ Params: { enterpriseId: string } }>(
    '/:enterpriseId/tags',
    {
      schema: {
        tags: ['enterprise-tag'],
        summary: '获取企业标签列表',
        description: '获取企业的所有标签，包括每个标签的使用用户数量',
        params: {
          type: 'object',
          properties: {
            enterpriseId: { type: 'string', description: '企业ID' },
          },
          required: ['enterpriseId'],
        },
        response: FullResponseSchema(GetEnterpriseTagsResponseSchema),
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { enterpriseId } = request.params
      const operatorId = (request as any).user?.id

      const result = await enterpriseTagService.getEnterpriseTags(enterpriseId, operatorId)
      return reply.send(successResponse(result))
    },
  )

  // 删除企业标签
  fastify.delete<{ Params: { enterpriseId: string; tagId: string } }>(
    '/:enterpriseId/tags/:tagId',
    {
      schema: {
        tags: ['enterprise-tag'],
        summary: '删除企业标签',
        description: '删除企业标签，同时会移除所有用户的该标签',
        params: {
          type: 'object',
          properties: {
            enterpriseId: { type: 'string', description: '企业ID' },
            tagId: { type: 'string', description: '标签ID' },
          },
          required: ['enterpriseId', 'tagId'],
        },
        response: FullResponseSchema(SuccessResponseSchema),
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { enterpriseId, tagId } = request.params
      const operatorId = (request as any).user?.id

      await enterpriseTagService.deleteEnterpriseTag(enterpriseId, tagId, operatorId)
      return reply.send(successResponseWithoutData('delete tag success'))
    },
  )

  // 给用户添加标签
  fastify.post<{ Params: { membershipId: string }; Body: AddTagToUserRequest }>(
    '/members/:membershipId/tags',
    {
      schema: {
        tags: ['enterprise-tag'],
        summary: '给用户添加标签',
        description: '为企业内的用户添加标签',
        params: {
          type: 'object',
          properties: {
            membershipId: { type: 'string', description: '会员ID' },
          },
          required: ['membershipId'],
        },
        body: AddTagToUserRequestSchema,
        response: FullResponseSchema(AddTagSuccessResponseSchema),
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { membershipId } = request.params
      const { tagId } = request.body
      const operatorId = (request as any).user?.id

      const result = await enterpriseTagService.addTagToUser(membershipId, tagId, operatorId)
      return reply.status(201).send(successResponse(result, 'add tag success'))
    },
  )

  // 移除用户标签
  fastify.delete<{ Params: { membershipId: string }; Body: RemoveTagFromUserRequest }>(
    '/members/:membershipId/tags',
    {
      schema: {
        tags: ['enterprise-tag'],
        summary: '移除用户标签',
        description: '移除用户的指定标签',
        params: {
          type: 'object',
          properties: {
            membershipId: { type: 'string', description: '会员ID' },
          },
          required: ['membershipId'],
        },
        body: RemoveTagFromUserRequestSchema,
        response: FullResponseSchema(SuccessResponseSchema),
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { membershipId } = request.params
      const { tagId } = request.body
      const operatorId = (request as any).user?.id

      await enterpriseTagService.removeTagFromUser(membershipId, tagId, operatorId)
      return reply.send(successResponseWithoutData('rmove tag success'))
    },
  )

  // 获取用户的所有标签
  fastify.get<{ Params: { membershipId: string } }>(
    '/members/:membershipId/tags',
    {
      schema: {
        tags: ['enterprise-tag'],
        summary: '获取成员标签列表',
        description: '获取企业成员的所有标签，直接返回标签数组',
        params: {
          type: 'object',
          properties: {
            membershipId: { type: 'string', description: '会员ID' },
          },
          required: ['membershipId'],
        },
        response: FullResponseSchema(GetUserTagsResponseSchema),
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { membershipId } = request.params
      const operatorId = (request as any).user?.id

      const result = await enterpriseTagService.getMembershipTags(membershipId, operatorId)
      return reply.send(successResponse(result))
    },
  )
}

export default router
