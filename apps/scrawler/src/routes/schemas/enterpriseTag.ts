import { Static, Type } from '@sinclair/typebox'

// ==================== 企业标签管理 ====================

// 创建企业标签请求 Schema
export const CreateEnterpriseTagRequestSchema = Type.Object(
  {
    name: Type.String({
      description: '标签名称',
      minLength: 1,
      maxLength: 30,
    }),
    color: Type.Optional(
      Type.String({
        description: '标签颜色',
        default: 'BLUE',
      }),
    ),
  },
  {
    additionalProperties: false,
  },
)

export type CreateEnterpriseTagRequest = Static<typeof CreateEnterpriseTagRequestSchema>

export const EnterpriseTagSchema = Type.Object({
  id: Type.String({ description: '标签ID' }),
  name: Type.String({ description: '标签名称' }),
  color: Type.String({ description: '标签颜色' }),
  createdAt: Type.String({ description: '创建时间', format: 'date-time' }),
  updatedAt: Type.String({ description: '更新时间', format: 'date-time' }),
})

export type EnterpriseTag = Static<typeof EnterpriseTagSchema>

export const GetEnterpriseTagsResponseSchema = Type.Array(EnterpriseTagSchema, {
  description: '标签列表',
})

export type GetEnterpriseTagsResponse = Static<typeof GetEnterpriseTagsResponseSchema>

// 分页信息 Schema
export const PaginationSchema = Type.Object({
  page: Type.Number({ description: '当前页码' }),
  pageSize: Type.Number({ description: '每页大小' }),
  total: Type.Number({ description: '总数量' }),
  totalPages: Type.Number({ description: '总页数' }),
  hasMore: Type.Boolean({ description: '是否有更多数据' }),
})

export type Pagination = Static<typeof PaginationSchema>

// ==================== 用户标签管理 ====================

// 给用户添加标签请求 Schema
export const AddTagToUserRequestSchema = Type.Object(
  {
    tagId: Type.String({ description: '标签ID' }),
  },
  {
    additionalProperties: false,
  },
)

export type AddTagToUserRequest = Static<typeof AddTagToUserRequestSchema>

// 移除用户标签请求 Schema
export const RemoveTagFromUserRequestSchema = Type.Object(
  {
    tagId: Type.String({ description: '标签ID' }),
  },
  {
    additionalProperties: false,
  },
)

export type RemoveTagFromUserRequest = Static<typeof RemoveTagFromUserRequestSchema>

// 用户标签信息 Schema
export const UserTagSchema = Type.Object({
  id: Type.String({ description: '标签ID' }),
  name: Type.String({ description: '标签名称' }),
  color: Type.String({ description: '标签颜色' }),
  taggedAt: Type.String({ description: '打标签时间', format: 'date-time' }),
  taggedBy: Type.String({ description: '打标签的操作者ID' }),
})

export type UserTag = Static<typeof UserTagSchema>

// 获取用户标签响应 Schema - 直接返回标签数组
export const GetUserTagsResponseSchema = Type.Array(UserTagSchema, {
  description: '用户的标签列表',
})

export type GetUserTagsResponse = Static<typeof GetUserTagsResponseSchema>

// ==================== 通用响应 ====================

// 成功响应（无数据）Schema
export const SuccessResponseSchema = Type.Object({
  success: Type.Boolean({ description: '操作是否成功' }),
})

export type SuccessResponse = Static<typeof SuccessResponseSchema>

export const AddTagSuccessResponseSchema = Type.Object({
  id: Type.String({ description: '用户标签关联ID' }),
  userMembershipId: Type.String({ description: '用户会员ID' }),
  tagId: Type.String({ description: '标签ID' }),
  createdBy: Type.String({ description: '创建者ID' }),
  createdAt: Type.String({ description: '创建时间', format: 'date-time' }),
  enterpriseTag: Type.Object({
    id: Type.String({ description: '标签ID' }),
    name: Type.String({ description: '标签名称' }),
    color: Type.String({ description: '标签颜色' }),
  }),
})

export type AddTagSuccessResponse = Static<typeof AddTagSuccessResponseSchema>
