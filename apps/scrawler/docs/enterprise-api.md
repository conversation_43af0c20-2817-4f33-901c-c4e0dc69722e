# 企业管理 API 接口文档

## 1. 创建企业
**POST** `/api/enterprise/`

创建新的企业账户，包括企业基本信息和初始配额设置。

---

## 2. 搜索企业成员列表
**POST** `/api/enterprise/:enterpriseId/members`

搜索企业成员列表，支持按标签筛选和分页查询。

**参数说明：**
- `enterpriseId`: 企业ID（路径参数）
- 请求体支持标签筛选和分页参数

---

## 3. 添加企业成员
**POST** `/api/enterprise/:id/members`

向企业添加新成员，通过邮箱列表批量添加用户。

**参数说明：**
- `id`: 企业ID（路径参数）
- 请求体包含邮箱列表

---

## 4. 设置企业管理员
**POST** `/api/enterprise/:enterpriseId/members/:memberShipId/admin`

将指定的企业成员设置为管理员权限。

**参数说明：**
- `enterpriseId`: 企业ID（路径参数）
- `memberShipId`: 成员ID（路径参数）

---

## 5. 开启成员配额限制
**POST** `/api/enterprise/:enterpriseId/members/:userId/enable-quota-limit`

为指定企业成员开启每日配额使用限制。

**参数说明：**
- `enterpriseId`: 企业ID（路径参数）
- `userId`: 用户ID（路径参数）

---

## 6. 关闭成员配额限制
**POST** `/api/enterprise/:enterpriseId/members/:userId/disable-quota-limit`

为指定企业成员关闭每日配额使用限制。

**参数说明：**
- `enterpriseId`: 企业ID（路径参数）
- `userId`: 用户ID（路径参数）

---

## 7. 购买信息卡会员
**POST** `/api/enterprise/infoCard/buy`

企业管理员为企业成员购买信息卡会员服务。

**参数说明：**
- 请求体包含购买信息和目标成员信息
