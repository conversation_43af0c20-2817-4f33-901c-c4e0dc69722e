-- CreateTable
CREATE TABLE "EnterpriseTag" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "color" TEXT NOT NULL DEFAULT 'BLUE',
    "enterpriseId" TEXT NOT NULL,
    "createdBy" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "EnterpriseTag_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserMembershipTag" (
    "id" TEXT NOT NULL,
    "userMembershipId" TEXT NOT NULL,
    "tagId" TEXT NOT NULL,
    "createdBy" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "UserMembershipTag_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "EnterpriseTag_enterpriseId_idx" ON "EnterpriseTag"("enterpriseId");

-- CreateIndex
CREATE UNIQUE INDEX "EnterpriseTag_enterpriseId_name_key" ON "EnterpriseTag"("enterpriseId", "name");

-- CreateIndex
CREATE INDEX "UserMembershipTag_userMembershipId_idx" ON "UserMembershipTag"("userMembershipId");

-- CreateIndex
CREATE INDEX "UserMembershipTag_tagId_idx" ON "UserMembershipTag"("tagId");

-- CreateIndex
CREATE UNIQUE INDEX "UserMembershipTag_userMembershipId_tagId_key" ON "UserMembershipTag"("userMembershipId", "tagId");
